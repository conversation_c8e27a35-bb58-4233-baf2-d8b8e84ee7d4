const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const logging = require('../common/logging')
const PhysicalActivityModel = require('../models/physical-activity-model')
const PhysicalActivityUtils = require('../utils/physical-activity-utils')

const physicalActivityContainer = 'PhysicalActivity'

class PhysicalActivityRepository {
  // Get activities with advanced filtering and aggregation for dashboard
  async getActivitiesForDashboard(
    patientId,
    dateFilter,
    customDateRange = null,
  ) {
    try {
      let query = `SELECT * FROM c WHERE c.patientId = '${patientId}' AND c.isActive = true`

      // Apply date filtering
      if (dateFilter && dateFilter !== 'all') {
        const dateRange = this.getDateRange(dateFilter, customDateRange)
        query += ` AND c.date >= '${dateRange.startDate}' AND c.date <= '${dateRange.endDate}'`
      }

      query += ` ORDER BY c.date ASC`

      const results = await cosmosDbContext.queryItems(
        query,
        physicalActivityContainer,
      )
      return results.map((item) => new PhysicalActivityModel(item))
    } catch (error) {
      logging.logError(
        `Failed to get activities for dashboard: ${error.message}`,
        error,
      )
      throw new Error('Failed to get activities for dashboard')
    }
  }

  // Get activity distribution data for pie charts
  async getActivityDistribution(patientId, dateFilter, customDateRange = null) {
    try {
      const activities = await this.getActivitiesForDashboard(
        patientId,
        dateFilter,
        customDateRange,
      )

      // Group by activity type and calculate totals
      const distribution = {}
      let totalDuration = 0

      activities.forEach((activity) => {
        if (!distribution[activity.activityType]) {
          distribution[activity.activityType] = {
            duration: 0,
            count: 0,
            caloriesBurned: 0,
          }
        }
        distribution[activity.activityType].duration += activity.duration
        distribution[activity.activityType].count += 1
        distribution[activity.activityType].caloriesBurned +=
          activity.caloriesBurned
        totalDuration += activity.duration
      })

      // Convert to percentage format
      const result = Object.keys(distribution).map((activityType) => ({
        activityType,
        duration: distribution[activityType].duration,
        percentage:
          totalDuration > 0
            ? Math.round(
                (distribution[activityType].duration / totalDuration) * 100,
              )
            : 0,
        count: distribution[activityType].count,
        caloriesBurned: distribution[activityType].caloriesBurned,
      }))

      return result
    } catch (error) {
      logging.logError(
        `Failed to get activity distribution: ${error.message}`,
        error,
      )
      throw new Error('Failed to get activity distribution')
    }
  }

  // Get intensity distribution data
  async getIntensityDistribution(
    patientId,
    dateFilter,
    customDateRange = null,
  ) {
    try {
      const activities = await this.getActivitiesForDashboard(
        patientId,
        dateFilter,
        customDateRange,
      )

      const distribution = {}
      let totalDuration = 0

      activities.forEach((activity) => {
        if (!distribution[activity.intensity]) {
          distribution[activity.intensity] = {
            duration: 0,
            count: 0,
          }
        }
        distribution[activity.intensity].duration += activity.duration
        distribution[activity.intensity].count += 1
        totalDuration += activity.duration
      })

      // Convert to percentage format
      const result = Object.keys(distribution).map((intensity) => ({
        intensity,
        duration: distribution[intensity].duration,
        percentage:
          totalDuration > 0
            ? Math.round(
                (distribution[intensity].duration / totalDuration) * 100,
              )
            : 0,
        count: distribution[intensity].count,
      }))

      return result
    } catch (error) {
      logging.logError(
        `Failed to get intensity distribution: ${error.message}`,
        error,
      )
      throw new Error('Failed to get intensity distribution')
    }
  }

  // Get day-wise chart data for line/bar charts
  async getDayWiseChartData(patientId, dateFilter, customDateRange = null) {
    try {
      const activities = await this.getActivitiesForDashboard(
        patientId,
        dateFilter,
        customDateRange,
      )
      const dateRange = PhysicalActivityUtils.getDateRange(
        dateFilter,
        customDateRange,
      )

      // Calculate daily aggregates using utility function
      const dailyAggregates =
        PhysicalActivityUtils.calculateDailyAggregates(activities)

      // Fill missing dates and calculate percentages
      const filledData = PhysicalActivityUtils.fillMissingDates(
        dailyAggregates,
        dateRange.startDate,
        dateRange.endDate,
      )
      const dataWithIntensityPercentages =
        PhysicalActivityUtils.calculateIntensityPercentages(filledData)
      const finalData = PhysicalActivityUtils.calculateActivityTypePercentages(
        dataWithIntensityPercentages,
      )

      return finalData
    } catch (error) {
      logging.logError(
        `Failed to get day-wise chart data: ${error.message}`,
        error,
      )
      throw new Error('Failed to get day-wise chart data')
    }
  }

  // Get specific chart data formats
  async getMetMinutesChartData(patientId, dateFilter, customDateRange = null) {
    try {
      const activities = await this.getActivitiesForDashboard(
        patientId,
        dateFilter,
        customDateRange,
      )
      const dateRange = PhysicalActivityUtils.getDateRange(
        dateFilter,
        customDateRange,
      )
      const dailyAggregates =
        PhysicalActivityUtils.calculateDailyAggregates(activities)

      return PhysicalActivityUtils.getMetMinutesChartData(
        dailyAggregates,
        dateRange.startDate,
        dateRange.endDate,
      )
    } catch (error) {
      logging.logError(
        `Failed to get MET minutes chart data: ${error.message}`,
        error,
      )
      throw new Error('Failed to get MET minutes chart data')
    }
  }

  async getTotalDurationChartData(
    patientId,
    dateFilter,
    customDateRange = null,
  ) {
    try {
      const activities = await this.getActivitiesForDashboard(
        patientId,
        dateFilter,
        customDateRange,
      )
      const dateRange = PhysicalActivityUtils.getDateRange(
        dateFilter,
        customDateRange,
      )
      const dailyAggregates =
        PhysicalActivityUtils.calculateDailyAggregates(activities)

      return PhysicalActivityUtils.getTotalDurationChartData(
        dailyAggregates,
        dateRange.startDate,
        dateRange.endDate,
      )
    } catch (error) {
      logging.logError(
        `Failed to get total duration chart data: ${error.message}`,
        error,
      )
      throw new Error('Failed to get total duration chart data')
    }
  }

  async getIntensityStackedChartData(
    patientId,
    dateFilter,
    customDateRange = null,
  ) {
    try {
      const activities = await this.getActivitiesForDashboard(
        patientId,
        dateFilter,
        customDateRange,
      )
      const dateRange = PhysicalActivityUtils.getDateRange(
        dateFilter,
        customDateRange,
      )
      const dailyAggregates =
        PhysicalActivityUtils.calculateDailyAggregates(activities)

      return PhysicalActivityUtils.getIntensityStackedChartData(
        dailyAggregates,
        dateRange.startDate,
        dateRange.endDate,
      )
    } catch (error) {
      logging.logError(
        `Failed to get intensity stacked chart data: ${error.message}`,
        error,
      )
      throw new Error('Failed to get intensity stacked chart data')
    }
  }

  async getActivityTypeStackedChartData(
    patientId,
    dateFilter,
    customDateRange = null,
  ) {
    try {
      const activities = await this.getActivitiesForDashboard(
        patientId,
        dateFilter,
        customDateRange,
      )
      const dateRange = PhysicalActivityUtils.getDateRange(
        dateFilter,
        customDateRange,
      )
      const dailyAggregates =
        PhysicalActivityUtils.calculateDailyAggregates(activities)

      return PhysicalActivityUtils.getActivityTypeStackedChartData(
        dailyAggregates,
        dateRange.startDate,
        dateRange.endDate,
      )
    } catch (error) {
      logging.logError(
        `Failed to get activity type stacked chart data: ${error.message}`,
        error,
      )
      throw new Error('Failed to get activity type stacked chart data')
    }
  }

  // Helper method to get date range (kept for backward compatibility)
  getDateRange(filter, customDateRange = null) {
    return PhysicalActivityUtils.getDateRange(filter, customDateRange)
  }
}

module.exports = new PhysicalActivityRepository()
