const physicalActivityService = require('../services/physical-activity-service')
const physicalActivityRepository = require('../repositories/physical-activity-repository')
const physicalActivityLookupService = require('../services/physical-activity-lookup-service')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')
const logging = require('../common/logging')

class PhysicalActivityHandler {
  // Get dashboard summary data
  async getDashboardSummary(req) {
    try {
      const patientId = req.query.get('patientId')
      const dateFilter = req.query.get('dateFilter') || 'last_15_days'
      const customStartDate = req.query.get('customStartDate') || null
      const customEndDate = req.query.get('customEndDate') || null

      if (!patientId) {
        return jsonResponse(
          'Missing required parameter: patientId',
          HttpStatusCode.BadRequest,
        )
      }

      const customDateRange =
        dateFilter === 'custom' && customStartDate && customEndDate
          ? { start: customStartDate, end: customEndDate }
          : null

      const summary = await physicalActivityService.getDashboardSummary(
        patientId,
        dateFilter,
        customDateRange,
      )
      return jsonResponse(summary)
    } catch (error) {
      logging.logError('Error getting dashboard summary:', error)
      return jsonResponse(
        'Failed to get dashboard summary',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  // Get activity distribution for pie chart
  async getActivityDistribution(req) {
    try {
      const patientId = req.query.get('patientId')
      const dateFilter = req.query.get('dateFilter') || 'last_15_days'
      const customStartDate = req.query.get('customStartDate') || null
      const customEndDate = req.query.get('customEndDate') || null

      if (!patientId) {
        return jsonResponse(
          'Missing required parameter: patientId',
          HttpStatusCode.BadRequest,
        )
      }

      const customDateRange =
        dateFilter === 'custom' && customStartDate && customEndDate
          ? { start: customStartDate, end: customEndDate }
          : null

      const distribution =
        await physicalActivityRepository.getActivityDistribution(
          patientId,
          dateFilter,
          customDateRange,
        )
      return jsonResponse(distribution)
    } catch (error) {
      logging.logError('Error getting activity distribution:', error)
      return jsonResponse(
        'Failed to get activity distribution',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  // Get intensity distribution for pie chart
  async getIntensityDistribution(req) {
    try {
      const patientId = req.query.get('patientId')
      const dateFilter = req.query.get('dateFilter') || 'last_15_days'
      const customStartDate = req.query.get('customStartDate') || null
      const customEndDate = req.query.get('customEndDate') || null

      if (!patientId) {
        return jsonResponse(
          'Missing required parameter: patientId',
          HttpStatusCode.BadRequest,
        )
      }

      const customDateRange =
        dateFilter === 'custom' && customStartDate && customEndDate
          ? { start: customStartDate, end: customEndDate }
          : null

      const distribution =
        await physicalActivityRepository.getIntensityDistribution(
          patientId,
          dateFilter,
          customDateRange,
        )
      return jsonResponse(distribution)
    } catch (error) {
      logging.logError('Error getting intensity distribution:', error)
      return jsonResponse(
        'Failed to get intensity distribution',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  // Get day-wise chart data for line/bar charts
  async getDayWiseChartData(req) {
    try {
      const patientId = req.query.get('patientId')
      const dateFilter = req.query.get('dateFilter') || 'last_15_days'
      const customStartDate = req.query.get('customStartDate') || null
      const customEndDate = req.query.get('customEndDate') || null

      if (!patientId) {
        return jsonResponse(
          'Missing required parameter: patientId',
          HttpStatusCode.BadRequest,
        )
      }

      const customDateRange =
        dateFilter === 'custom' && customStartDate && customEndDate
          ? { start: customStartDate, end: customEndDate }
          : null

      const chartData = await physicalActivityRepository.getDayWiseChartData(
        patientId,
        dateFilter,
        customDateRange,
      )
      return jsonResponse(chartData)
    } catch (error) {
      logging.logError('Error getting day-wise chart data:', error)
      return jsonResponse(
        'Failed to get day-wise chart data',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  // Get complete dashboard data in one call
  async getCompleteDashboard(req) {
    try {
      const patientId = req.query.get('patientId')
      const dateFilter = req.query.get('dateFilter') || 'last_15_days'
      const customStartDate = req.query.get('customStartDate') || null
      const customEndDate = req.query.get('customEndDate') || null

      if (!patientId) {
        return jsonResponse(
          'Missing required parameter: patientId',
          HttpStatusCode.BadRequest,
        )
      }

      const customDateRange =
        dateFilter === 'custom' && customStartDate && customEndDate
          ? { start: customStartDate, end: customEndDate }
          : null

      // Get all dashboard data in parallel
      const [
        summary,
        activityDistribution,
        intensityDistribution,
        dayWiseData,
        metMinutesChart,
        totalDurationChart,
        intensityStackedChart,
        activityTypeStackedChart,
      ] = await Promise.all([
        physicalActivityService.getDashboardSummary(
          patientId,
          dateFilter,
          customDateRange,
        ),
        physicalActivityRepository.getActivityDistribution(
          patientId,
          dateFilter,
          customDateRange,
        ),
        physicalActivityRepository.getIntensityDistribution(
          patientId,
          dateFilter,
          customDateRange,
        ),
        physicalActivityRepository.getDayWiseChartData(
          patientId,
          dateFilter,
          customDateRange,
        ),
        physicalActivityRepository.getMetMinutesChartData(
          patientId,
          dateFilter,
          customDateRange,
        ),
        physicalActivityRepository.getTotalDurationChartData(
          patientId,
          dateFilter,
          customDateRange,
        ),
        physicalActivityRepository.getIntensityStackedChartData(
          patientId,
          dateFilter,
          customDateRange,
        ),
        physicalActivityRepository.getActivityTypeStackedChartData(
          patientId,
          dateFilter,
          customDateRange,
        ),
      ])

      const dashboardData = {
        summary,
        activityDistribution,
        intensityDistribution,
        dayWiseData,
        charts: {
          metMinutes: metMinutesChart,
          totalDuration: totalDurationChart,
          intensityStacked: intensityStackedChart,
          activityTypeStacked: activityTypeStackedChart,
        },
        dateFilter,
        customDateRange,
      }

      return jsonResponse(dashboardData)
    } catch (error) {
      logging.logError('Error getting complete dashboard:', error)
      return jsonResponse(
        'Failed to get complete dashboard',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  // Get available activities and their details
  async getAvailableActivities(req) {
    try {
      const activityType = req.query.get('activityType') || null
      const searchTerm = req.query.get('search') || null

      let activities
      if (searchTerm) {
        activities = physicalActivityLookupService.searchActivities(searchTerm)
      } else if (activityType) {
        activities =
          physicalActivityLookupService.getActivitiesByType(activityType)
      } else {
        activities =
          physicalActivityLookupService.getActivitiesWithIntensities()
      }

      return jsonResponse({
        activities,
        activityTypes: physicalActivityLookupService.getActivityTypes(),
      })
    } catch (error) {
      logging.logError('Error getting available activities:', error)
      return jsonResponse(
        'Failed to get available activities',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  // Get patient's activity records with filtering
  async getPatientActivities(req) {
    try {
      const patientId = req.query.get('patientId')
      const dateFilter = req.query.get('dateFilter') || 'last_30_days'
      const customStartDate = req.query.get('customStartDate') || null
      const customEndDate = req.query.get('customEndDate') || null
      const activityType = req.query.get('activityType') || null
      const intensity = req.query.get('intensity') || null

      if (!patientId) {
        return jsonResponse(
          'Missing required parameter: patientId',
          HttpStatusCode.BadRequest,
        )
      }

      const customDateRange =
        dateFilter === 'custom' && customStartDate && customEndDate
          ? { start: customStartDate, end: customEndDate }
          : null

      let activities =
        await physicalActivityService.getPhysicalActivitiesByPatient(
          patientId,
          dateFilter,
          customDateRange,
        )

      // Apply additional filters
      if (activityType) {
        activities = activities.filter(
          (activity) =>
            activity.activityType.toLowerCase() === activityType.toLowerCase(),
        )
      }

      if (intensity) {
        activities = activities.filter(
          (activity) =>
            activity.intensity.toLowerCase() === intensity.toLowerCase(),
        )
      }

      return jsonResponse({
        activities,
        totalCount: activities.length,
        filters: {
          dateFilter,
          customDateRange,
          activityType,
          intensity,
        },
      })
    } catch (error) {
      logging.logError('Error getting patient activities:', error)
      return jsonResponse(
        'Failed to get patient activities',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  // Create new physical activity record
  async createPhysicalActivity(req) {
    try {
      const requestBody = await req.json()
      const {
        patientId,
        organizationId,
        activityType,
        activity,
        intensity,
        duration,
        date,
        notes,
      } = requestBody

      // Validate required fields
      if (
        !patientId ||
        !organizationId ||
        !activityType ||
        !activity ||
        !intensity ||
        !duration
      ) {
        return jsonResponse(
          'Missing required fields: patientId, organizationId, activityType, activity, intensity, duration',
          HttpStatusCode.BadRequest,
        )
      }

      // Get MET value for the activity and intensity
      const metValue = physicalActivityLookupService.getMetValue(
        activity,
        intensity,
        activityType,
      )
      if (!metValue) {
        return jsonResponse(
          `Invalid activity and intensity combination: ${activity} at ${intensity} intensity`,
          HttpStatusCode.BadRequest,
        )
      }

      const activityData = {
        patientId,
        organizationId,
        activityType,
        activity,
        intensity,
        duration: parseFloat(duration),
        date: date || new Date().toISOString().split('T')[0],
        metValue,
        notes: notes || '',
      }

      const result = await physicalActivityService.createPhysicalActivity(
        activityData,
      )
      return jsonResponse(result, HttpStatusCode.Created)
    } catch (error) {
      logging.logError('Error creating physical activity:', error)
      return jsonResponse(
        'Failed to create physical activity',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  // Update physical activity record
  async updatePhysicalActivity(req) {
    try {
      const activityId = req.query.get('activityId')
      if (!activityId) {
        return jsonResponse(
          'Missing required parameter: activityId',
          HttpStatusCode.BadRequest,
        )
      }

      const requestBody = await req.json()
      const { activityType, activity, intensity, duration, date, notes } =
        requestBody

      // If activity or intensity is being updated, validate and get new MET value
      if (activity && intensity) {
        const metValue = physicalActivityLookupService.getMetValue(
          activity,
          intensity,
          activityType,
        )
        if (!metValue) {
          return jsonResponse(
            `Invalid activity and intensity combination: ${activity} at ${intensity} intensity`,
            HttpStatusCode.BadRequest,
          )
        }
        requestBody.metValue = metValue
      }

      if (duration) {
        requestBody.duration = parseFloat(duration)
      }

      const result = await physicalActivityService.updatePhysicalActivity(
        activityId,
        requestBody,
      )
      return jsonResponse(result)
    } catch (error) {
      logging.logError('Error updating physical activity:', error)
      return jsonResponse(
        'Failed to update physical activity',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  // Delete physical activity record
  async deletePhysicalActivity(req) {
    try {
      const activityId = req.query.get('activityId')
      if (!activityId) {
        return jsonResponse(
          'Missing required parameter: activityId',
          HttpStatusCode.BadRequest,
        )
      }

      const result = await physicalActivityService.deletePhysicalActivity(
        activityId,
      )
      return jsonResponse({
        message: 'Physical activity deleted successfully',
        result,
      })
    } catch (error) {
      logging.logError('Error deleting physical activity:', error)
      return jsonResponse(
        'Failed to delete physical activity',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  // Get single physical activity record
  async getPhysicalActivityById(req) {
    try {
      const activityId = req.query.get('activityId')
      if (!activityId) {
        return jsonResponse(
          'Missing required parameter: activityId',
          HttpStatusCode.BadRequest,
        )
      }

      const activity = await physicalActivityService.getPhysicalActivityById(
        activityId,
      )
      if (!activity) {
        return jsonResponse(
          'Physical activity not found',
          HttpStatusCode.NotFound,
        )
      }

      return jsonResponse(activity)
    } catch (error) {
      logging.logError('Error getting physical activity:', error)
      return jsonResponse(
        'Failed to get physical activity',
        HttpStatusCode.InternalServerError,
      )
    }
  }
}

module.exports = new PhysicalActivityHandler()
