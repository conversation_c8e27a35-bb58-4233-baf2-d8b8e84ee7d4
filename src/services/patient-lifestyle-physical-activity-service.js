const patientLifestylePhysicalActivityRepository = require('../repositories/patient-lifestyle-physical-activity-repository')
const patientService = require('./patient-service')
const logging = require('../common/logging')

class PatientLifestylePhysicalActivityService {
  // Get patient weight from profile
  async getPatientWeight(patientId) {
    try {
      const patient = await patientService.GetPatientProfile(patientId)
      const weight = patient?.weight ? parseFloat(patient.weight) : 70 // Default to 70kg
      return weight > 0 ? weight : 70
    } catch (error) {
      logging.logInfo(
        `Failed to get patient weight for ${patientId}, using default 70kg: ${error.message}`,
      )
      return 70
    }
  }

  // Get dashboard summary statistics
  async getDashboardSummary(patientId, dateFilter, customDateRange = null) {
    try {
      const patientWeight = await this.getPatientWeight(patientId)
      const activities =
        await patientLifestylePhysicalActivityRepository.getProcessedPhysicalActivityData(
          patientId,
          dateFilter,
          customDateRange,
          patientWeight,
        )

      if (!activities || activities.length === 0) {
        return {
          totalCaloriesSpent: 0,
          totalSessions: 0,
          totalDuration: 0,
          avgCaloriesSpent: 0,
          avgIntensity: 'N/A',
          avgDuration: 0,
        }
      }

      const totalCaloriesSpent = activities.reduce(
        (sum, activity) => sum + activity.caloriesBurned,
        0,
      )
      const totalSessions = activities.length
      const totalDuration = activities.reduce(
        (sum, activity) => sum + activity.duration,
        0,
      )
      const avgCaloriesSpent = Math.round(totalCaloriesSpent / totalSessions)
      const avgDuration = Math.round(totalDuration / totalSessions)

      // Calculate average intensity
      const intensityValues = { mild: 1, moderate: 2, intense: 3 }
      const avgIntensityValue =
        activities.reduce((sum, activity) => {
          return sum + (intensityValues[activity.intensity.toLowerCase()] || 1)
        }, 0) / totalSessions

      let avgIntensity = 'Mild'
      if (avgIntensityValue >= 2.5) avgIntensity = 'Intense'
      else if (avgIntensityValue >= 1.5) avgIntensity = 'Moderate'

      return {
        totalCaloriesSpent: Math.round(totalCaloriesSpent),
        totalSessions,
        totalDuration: Math.round(totalDuration),
        avgCaloriesSpent,
        avgIntensity,
        avgDuration,
      }
    } catch (error) {
      logging.logError(
        `Failed to get dashboard summary for patient ${patientId}:`,
        error,
      )
      throw new Error('Failed to calculate dashboard summary')
    }
  }

  // Get activity distribution for pie charts
  async getActivityDistribution(patientId, dateFilter, customDateRange = null) {
    try {
      const patientWeight = await this.getPatientWeight(patientId)
      const activities =
        await patientLifestylePhysicalActivityRepository.getProcessedPhysicalActivityData(
          patientId,
          dateFilter,
          customDateRange,
          patientWeight,
        )

      if (!activities || activities.length === 0) {
        return []
      }

      const distribution = {}
      let totalDuration = 0

      activities.forEach((activity) => {
        if (!distribution[activity.activityType]) {
          distribution[activity.activityType] = {
            duration: 0,
            count: 0,
          }
        }
        distribution[activity.activityType].duration += activity.duration
        distribution[activity.activityType].count += 1
        totalDuration += activity.duration
      })

      return Object.keys(distribution).map((activityType) => ({
        activityType,
        duration: Math.round(distribution[activityType].duration),
        percentage:
          totalDuration > 0
            ? Math.round(
                (distribution[activityType].duration / totalDuration) * 100,
              )
            : 0,
        count: distribution[activityType].count,
      }))
    } catch (error) {
      logging.logError(
        `Failed to get activity distribution for patient ${patientId}:`,
        error,
      )
      throw new Error('Failed to calculate activity distribution')
    }
  }

  // Get intensity distribution for pie charts
  async getIntensityDistribution(
    patientId,
    dateFilter,
    customDateRange = null,
  ) {
    try {
      const patientWeight = await this.getPatientWeight(patientId)
      const activities =
        await patientLifestylePhysicalActivityRepository.getProcessedPhysicalActivityData(
          patientId,
          dateFilter,
          customDateRange,
          patientWeight,
        )

      if (!activities || activities.length === 0) {
        return []
      }

      const distribution = {}
      let totalDuration = 0

      activities.forEach((activity) => {
        const intensity =
          activity.intensity.charAt(0).toUpperCase() +
          activity.intensity.slice(1).toLowerCase()
        if (!distribution[intensity]) {
          distribution[intensity] = {
            duration: 0,
            count: 0,
          }
        }
        distribution[intensity].duration += activity.duration
        distribution[intensity].count += 1
        totalDuration += activity.duration
      })

      return Object.keys(distribution).map((intensity) => ({
        intensity,
        duration: Math.round(distribution[intensity].duration),
        percentage:
          totalDuration > 0
            ? Math.round(
                (distribution[intensity].duration / totalDuration) * 100,
              )
            : 0,
        count: distribution[intensity].count,
      }))
    } catch (error) {
      logging.logError(
        `Failed to get intensity distribution for patient ${patientId}:`,
        error,
      )
      throw new Error('Failed to calculate intensity distribution')
    }
  }

  // Get day-wise aggregated data for charts
  async getDayWiseData(patientId, dateFilter, customDateRange = null) {
    try {
      const patientWeight = await this.getPatientWeight(patientId)
      const activities =
        await patientLifestylePhysicalActivityRepository.getProcessedPhysicalActivityData(
          patientId,
          dateFilter,
          customDateRange,
          patientWeight,
        )

      if (!activities || activities.length === 0) {
        return []
      }

      const dateRange = patientLifestylePhysicalActivityRepository.getDateRange(
        dateFilter,
        customDateRange,
      )
      const dailyData = {}

      // Initialize all dates in range with zero values
      const startDate = new Date(dateRange.startDate)
      const endDate = new Date(dateRange.endDate)

      for (
        let d = new Date(startDate);
        d <= endDate;
        d.setDate(d.getDate() + 1)
      ) {
        const dateStr = d.toISOString().split('T')[0]
        dailyData[dateStr] = {
          date: dateStr,
          totalDuration: 0,
          totalMetMinutes: 0,
          totalCalories: 0,
          activityTypes: {},
          intensities: {},
        }
      }

      // Aggregate activities by date
      activities.forEach((activity) => {
        const date = activity.date
        if (dailyData[date]) {
          dailyData[date].totalDuration += activity.duration
          dailyData[date].totalMetMinutes += activity.metMinutes
          dailyData[date].totalCalories += activity.caloriesBurned

          // Track activity types
          if (!dailyData[date].activityTypes[activity.activityType]) {
            dailyData[date].activityTypes[activity.activityType] = 0
          }
          dailyData[date].activityTypes[activity.activityType] +=
            activity.duration

          // Track intensities
          const intensity =
            activity.intensity.charAt(0).toUpperCase() +
            activity.intensity.slice(1).toLowerCase()
          if (!dailyData[date].intensities[intensity]) {
            dailyData[date].intensities[intensity] = 0
          }
          dailyData[date].intensities[intensity] += activity.duration
        }
      })

      // Convert to array and calculate percentages
      return Object.values(dailyData).map((day) => {
        const activityTypePercentages = {}
        const intensityPercentages = {}

        if (day.totalDuration > 0) {
          Object.keys(day.activityTypes).forEach((type) => {
            activityTypePercentages[type] = Math.round(
              (day.activityTypes[type] / day.totalDuration) * 100,
            )
          })
          Object.keys(day.intensities).forEach((intensity) => {
            intensityPercentages[intensity] = Math.round(
              (day.intensities[intensity] / day.totalDuration) * 100,
            )
          })
        }

        return {
          date: day.date,
          totalDuration: Math.round(day.totalDuration),
          totalMetMinutes: Math.round(day.totalMetMinutes),
          totalCalories: Math.round(day.totalCalories),
          activityTypePercentages,
          intensityPercentages,
        }
      })
    } catch (error) {
      logging.logError(
        `Failed to get day-wise data for patient ${patientId}:`,
        error,
      )
      throw new Error('Failed to calculate day-wise data')
    }
  }

  // Get activity records for table display
  async getActivityRecords(patientId, dateFilter, customDateRange = null) {
    try {
      const patientWeight = await this.getPatientWeight(patientId)
      const activities =
        await patientLifestylePhysicalActivityRepository.getProcessedPhysicalActivityData(
          patientId,
          dateFilter,
          customDateRange,
          patientWeight,
        )

      return activities.map((activity) => ({
        date: activity.date,
        activityType: activity.activityType,
        activity: activity.activity,
        intensity:
          activity.intensity.charAt(0).toUpperCase() +
          activity.intensity.slice(1).toLowerCase(),
        duration: activity.duration,
        caloriesBurned: Math.round(activity.caloriesBurned),
      }))
    } catch (error) {
      logging.logError(
        `Failed to get activity records for patient ${patientId}:`,
        error,
      )
      throw new Error('Failed to get activity records')
    }
  }
}

module.exports = new PatientLifestylePhysicalActivityService()
