const { app } = require('@azure/functions')
const patientLifestylePhysicalActivityHandler = require('../handlers/patient-lifestyle-physical-activity-handler')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')

app.http('physical-activity-dashboard', {
  methods: ['GET'],
  route: 'physical-activity/dashboard',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(
      `Complete Physical Activity Dashboard processed request for url "${req.url}"`,
    )

    try {
      const data =
        await patientLifestylePhysicalActivityHandler.getCompleteDashboard(req)
      return data
    } catch (err) {
      context.log('Error fetching complete dashboard:', err)
      return jsonResponse(
        'Error fetching complete dashboard',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

// Available Physical Activities (for dropdowns/selection)
app.http('physical-activity-list', {
  methods: ['GET'],
  route: 'physical-activity/list',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(
      `Available Physical Activities processed request for url "${req.url}"`,
    )

    try {
      const data =
        await patientLifestylePhysicalActivityHandler.getAvailableActivities(
          req,
        )
      return data
    } catch (err) {
      context.log('Error fetching available activities:', err)
      return jsonResponse(
        'Error fetching available activities',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})
