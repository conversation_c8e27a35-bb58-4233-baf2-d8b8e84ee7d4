const { app } = require('@azure/functions')
const physicalActivityHandler = require('../handlers/physical-activity-handler')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')
const { HttpMethod } = require('../common/constant')

// Physical Activity CRUD Operations
app.http('physical-activity-management', {
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  route: 'physical-activity/manage',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Physical Activity Management processed request for url "${req.url}"`)
    
    switch (req.method) {
      case HttpMethod.get:
        // Get single activity by ID or get patient activities with filtering
        const activityId = req.query.get('activityId')
        if (activityId) {
          return await physicalActivityHandler.getPhysicalActivityById(req)
        } else {
          return await physicalActivityHandler.getPatientActivities(req)
        }

      case HttpMethod.post:
        // Create new physical activity
        return await physicalActivityHandler.createPhysicalActivity(req)

      case HttpMethod.put:
        // Update physical activity
        return await physicalActivityHandler.updatePhysicalActivity(req)

      case HttpMethod.delete:
        // Delete physical activity
        return await physicalActivityHandler.deletePhysicalActivity(req)

      default:
        return jsonResponse('Method not allowed', HttpStatusCode.MethodNotAllowed)
    }
  }
})

// Bulk Physical Activity Operations
app.http('physical-activity-bulk', {
  methods: ['POST', 'DELETE'],
  route: 'physical-activity/bulk',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Physical Activity Bulk Operations processed request for url "${req.url}"`)
    
    try {
      const requestBody = await req.json()
      
      switch (req.method) {
        case HttpMethod.post:
          // Bulk create physical activities
          const { activities } = requestBody
          if (!activities || !Array.isArray(activities)) {
            return jsonResponse('Missing or invalid activities array', HttpStatusCode.BadRequest)
          }

          const results = []
          const errors = []

          for (let i = 0; i < activities.length; i++) {
            try {
              // Create a mock request object for each activity
              const mockReq = {
                json: async () => activities[i]
              }
              const result = await physicalActivityHandler.createPhysicalActivity(mockReq)
              results.push({ index: i, success: true, data: result })
            } catch (error) {
              errors.push({ index: i, error: error.message })
            }
          }

          return jsonResponse({
            message: 'Bulk create completed',
            totalProcessed: activities.length,
            successful: results.length,
            failed: errors.length,
            results,
            errors
          })

        case HttpMethod.delete:
          // Bulk delete physical activities
          const { activityIds } = requestBody
          if (!activityIds || !Array.isArray(activityIds)) {
            return jsonResponse('Missing or invalid activityIds array', HttpStatusCode.BadRequest)
          }

          const deleteResults = []
          const deleteErrors = []

          for (let i = 0; i < activityIds.length; i++) {
            try {
              // Create a mock request object for each activity ID
              const mockReq = {
                query: {
                  get: (key) => key === 'activityId' ? activityIds[i] : null
                }
              }
              const result = await physicalActivityHandler.deletePhysicalActivity(mockReq)
              deleteResults.push({ activityId: activityIds[i], success: true })
            } catch (error) {
              deleteErrors.push({ activityId: activityIds[i], error: error.message })
            }
          }

          return jsonResponse({
            message: 'Bulk delete completed',
            totalProcessed: activityIds.length,
            successful: deleteResults.length,
            failed: deleteErrors.length,
            results: deleteResults,
            errors: deleteErrors
          })

        default:
          return jsonResponse('Method not allowed', HttpStatusCode.MethodNotAllowed)
      }
    } catch (error) {
      context.log('Error in bulk operations:', error)
      return jsonResponse('Error processing bulk operation', HttpStatusCode.InternalServerError)
    }
  }
})
