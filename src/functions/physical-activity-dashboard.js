const { app } = require('@azure/functions')
const physicalActivityHandler = require('../handlers/physical-activity-handler')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')
const { HttpMethod } = require('../common/constant')

// Physical Activity Dashboard Summary
app.http('physical-activity-dashboard-summary', {
  methods: ['GET'],
  route: 'physical-activity/dashboard/summary',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Physical Activity Dashboard Summary processed request for url "${req.url}"`)
    
    try {
      const data = await physicalActivityHandler.getDashboardSummary(req)
      return data
    } catch (err) {
      context.log('Error fetching physical activity dashboard summary:', err)
      return jsonResponse('Error fetching dashboard summary', HttpStatusCode.InternalServerError)
    }
  }
})

// Physical Activity Distribution (Pie Chart)
app.http('physical-activity-distribution', {
  methods: ['GET'],
  route: 'physical-activity/dashboard/activity-distribution',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Physical Activity Distribution processed request for url "${req.url}"`)
    
    try {
      const data = await physicalActivityHandler.getActivityDistribution(req)
      return data
    } catch (err) {
      context.log('Error fetching activity distribution:', err)
      return jsonResponse('Error fetching activity distribution', HttpStatusCode.InternalServerError)
    }
  }
})

// Physical Activity Intensity Distribution (Pie Chart)
app.http('physical-activity-intensity-distribution', {
  methods: ['GET'],
  route: 'physical-activity/dashboard/intensity-distribution',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Physical Activity Intensity Distribution processed request for url "${req.url}"`)
    
    try {
      const data = await physicalActivityHandler.getIntensityDistribution(req)
      return data
    } catch (err) {
      context.log('Error fetching intensity distribution:', err)
      return jsonResponse('Error fetching intensity distribution', HttpStatusCode.InternalServerError)
    }
  }
})

// Physical Activity Day-wise Chart Data (Line/Bar Charts)
app.http('physical-activity-chart-data', {
  methods: ['GET'],
  route: 'physical-activity/dashboard/chart-data',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Physical Activity Chart Data processed request for url "${req.url}"`)
    
    try {
      const data = await physicalActivityHandler.getDayWiseChartData(req)
      return data
    } catch (err) {
      context.log('Error fetching chart data:', err)
      return jsonResponse('Error fetching chart data', HttpStatusCode.InternalServerError)
    }
  }
})

// Complete Physical Activity Dashboard (All data in one call)
app.http('physical-activity-complete-dashboard', {
  methods: ['GET'],
  route: 'physical-activity/dashboard/complete',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Complete Physical Activity Dashboard processed request for url "${req.url}"`)
    
    try {
      const data = await physicalActivityHandler.getCompleteDashboard(req)
      return data
    } catch (err) {
      context.log('Error fetching complete dashboard:', err)
      return jsonResponse('Error fetching complete dashboard', HttpStatusCode.InternalServerError)
    }
  }
})

// Get Available Activities and Activity Types
app.http('physical-activity-available-activities', {
  methods: ['GET'],
  route: 'physical-activity/available-activities',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Available Physical Activities processed request for url "${req.url}"`)
    
    try {
      const data = await physicalActivityHandler.getAvailableActivities(req)
      return data
    } catch (err) {
      context.log('Error fetching available activities:', err)
      return jsonResponse('Error fetching available activities', HttpStatusCode.InternalServerError)
    }
  }
})

// Get Patient's Physical Activity Records
app.http('physical-activity-patient-records', {
  methods: ['GET'],
  route: 'physical-activity/patient/records',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Patient Physical Activity Records processed request for url "${req.url}"`)
    
    try {
      const data = await physicalActivityHandler.getPatientActivities(req)
      return data
    } catch (err) {
      context.log('Error fetching patient activities:', err)
      return jsonResponse('Error fetching patient activities', HttpStatusCode.InternalServerError)
    }
  }
})
